# CcbLife Backend Service Deployment Script (Simplified)
# Usage:
#   .\ccblife-simple.ps1           # Docker deployment
#   .\ccblife-simple.ps1 jar       # JAR deployment with console output
#   .\ccblife-simple.ps1 stop      # Stop services

param(
    [string]$Action = "docker"
)

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Deploy-Jar-Simple {
    Write-Info "Starting JAR deployment..."
    
    # Check Java
    try {
        $javaVersion = java -version 2>&1 | Select-String "version"
        if ($javaVersion) {
            Write-Success "Java OK: $javaVersion"
        }
    } catch {
        Write-Error "Java not found! Please install Java 8+"
        return
    }
    
    # Check JAR file
    if (!(Test-Path "ccblife-admin/target/ccblife-admin.jar")) {
        Write-Info "Building project..."
        mvn clean package -DskipTests
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Build failed!"
            return
        }
    }
    
    # Create logs directory
    if (!(Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs" | Out-Null
    }
    
    Write-Info "Starting backend service in console mode..."
    Write-Info "Press Ctrl+C to stop the service"
    Write-Info "Service will start on http://localhost:8090"
    
    # Start JAR in foreground (console mode)
    $jvmArgs = @(
        "-Duser.timezone=Asia/Shanghai",
        "-Djava.awt.headless=true",
        "-Djava.security.egd=file:/dev/./urandom",
        "-Dfile.encoding=UTF-8",
        "-Xms512m",
        "-Xmx1024m",
        "-jar",
        "ccblife-admin/target/ccblife-admin.jar"
    )

    & java $jvmArgs
}

function Stop-Services {
    Write-Info "Stopping services..."
    
    # Stop Docker services
    if (Test-Path "docker-compose.yml") {
        docker-compose down
        Write-Success "Docker services stopped"
    }
    
    # Stop JAR processes
    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*ccblife-admin.jar*" }
    if ($javaProcesses) {
        $javaProcesses | Stop-Process -Force
        Write-Success "JAR processes stopped"
    }
}

function Deploy-Docker {
    Write-Info "Starting Docker deployment..."
    
    # Check Docker
    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker OK: $dockerVersion"
        }
    } catch {
        Write-Error "Docker not found or not running!"
        return
    }
    
    # Build and start
    Write-Info "Building and starting services..."
    docker-compose up --build -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Services started successfully!"
        Write-Info "Backend API: http://localhost:8090"
        Write-Info "Database: localhost:5432"
    } else {
        Write-Error "Failed to start services!"
    }
}

# Main logic
Write-Host "=========================================" -ForegroundColor Green
Write-Host "CcbLife Backend Service Deployment" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

switch ($Action.ToLower()) {
    "jar" {
        Deploy-Jar-Simple
    }
    "stop" {
        Stop-Services
    }
    default {
        Deploy-Docker
    }
}
