# CcbLife Backend Service Deployment Script for PowerShell
# Supports both Docker and traditional JAR deployment
#
# Usage:
#   .\ccblife.ps1           # Docker deployment (recommended)
#   .\ccblife.ps1 docker    # Docker deployment
#   .\ccblife.ps1 jar       # Traditional JAR deployment
#   .\ccblife.ps1 jar -Watch # JAR deployment with log monitoring
#   .\ccblife.ps1 stop      # Stop services
#   .\ccblife.ps1 status    # Check status

param(
    [string]$Action = "docker",
    [switch]$Watch = $false
)

# Color functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Show-Header {
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host "CcbLife Backend Service Deployment" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
}

function Test-Docker {
    Write-Info "Checking Docker environment..."
    try {
        $dockerVersion = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker OK: $dockerVersion"
            return $true
        }
    } catch {
        Write-Error "Docker not found or not running!"
        Write-Info "Please install Docker Desktop and make sure it's running."
        return $false
    }
    return $false
}

function Test-Java {
    Write-Info "Checking Java environment..."
    try {
        $javaVersion = java -version 2>&1 | Select-String "version"
        if ($javaVersion) {
            Write-Success "Java OK: $javaVersion"
            return $true
        }
    } catch {
        Write-Error "Java not found!"
        Write-Info "Please install Java 8+ for JAR deployment."
        return $false
    }
    return $false
}

function Build-Backend {
    Write-Info "Building backend project..."
    if (!(Test-Path "ccblife-admin/target/ccblife-admin.jar")) {
        Write-Warning "JAR file not found, building project..."
        mvn clean package -DskipTests
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Backend build failed!"
            return $false
        }
        Write-Success "Backend build completed"
    } else {
        Write-Success "JAR file already exists"
    }
    return $true
}

function Deploy-Docker {
    Write-Info "Starting Docker deployment..."
    
    if (!(Test-Docker)) {
        return $false
    }
    
    # Check required files
    if (!(Test-Path "docker-compose.yml")) {
        Write-Error "docker-compose.yml not found!"
        return $false
    }
    
    if (!(Build-Backend)) {
        return $false
    }
    
    # Stop existing containers
    Write-Info "Stopping existing containers..."
    docker-compose down --remove-orphans
    
    # Clean old images
    Write-Info "Cleaning old images..."
    docker image prune -f
    
    # Build and start services
    Write-Info "Building and starting services..."
    docker-compose up --build -d
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to start services!"
        return $false
    }
    
    # Wait for services
    Write-Info "Waiting for services to start..."
    Start-Sleep -Seconds 30
    
    # Health check
    Write-Info "Performing health check..."
    for ($i = 1; $i -le 30; $i++) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8090/captchaImage" -TimeoutSec 2 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Success "Backend service is running!"
                break
            }
        } catch {
            if ($i -eq 30) {
                Write-Error "Backend service failed to start!"
                docker-compose logs ccblife_server
                return $false
            }
            Write-Info "Waiting for backend service... ($i/30)"
            Start-Sleep -Seconds 2
        }
    }
    
    # Check database
    Write-Info "Checking database connection..."
    $dbCheck = docker exec ccblife-postgres pg_isready -U postgres 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Database connection OK"
    } else {
        Write-Warning "Database connection check failed"
    }
    
    return $true
}

function Deploy-Jar {
    Write-Info "Starting JAR deployment..."
    
    if (!(Test-Java)) {
        return $false
    }
    
    if (!(Build-Backend)) {
        return $false
    }
    
    # Create logs directory
    if (!(Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs" | Out-Null
    }
    
    # Start JAR
    Write-Info "Starting backend service in console mode..."
    Write-Info "Press Ctrl+C to stop the service"
    Write-Info "Service will start on http://localhost:8090"

    $jarPath = "ccblife-admin/target/ccblife-admin.jar"
    $jvmArgs = @(
        "-Duser.timezone=Asia/Shanghai",
        "-Djava.awt.headless=true",
        "-Djava.security.egd=file:/dev/./urandom",
        "-Dfile.encoding=UTF-8",
        "-Xms512m",
        "-Xmx1024m",
        "-jar",
        $jarPath
    )

    # Start JAR in foreground (console mode)
    & java $jvmArgs
}

function Stop-Services {
    Write-Info "Stopping services..."
    
    # Stop Docker services
    if (Test-Path "docker-compose.yml") {
        docker-compose down
        Write-Success "Docker services stopped"
    }
    
    # Stop JAR processes
    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*ccblife-admin.jar*" }
    if ($javaProcesses) {
        $javaProcesses | Stop-Process -Force
        Write-Success "JAR processes stopped"
    }
}

function Show-Status {
    Write-Info "Service Status:"
    
    # Docker status
    if (Test-Path "docker-compose.yml") {
        Write-Host "`nDocker Services:" -ForegroundColor Yellow
        docker-compose ps
    }
    
    # JAR status
    Write-Host "`nJava Processes:" -ForegroundColor Yellow
    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*ccblife-admin.jar*" }
    if ($javaProcesses) {
        $javaProcesses | Format-Table Id, ProcessName, CPU, WorkingSet -AutoSize
    } else {
        Write-Host "No JAR processes running"
    }
    
    # Port status
    Write-Host "`nPort Status:" -ForegroundColor Yellow
    $ports = @(8090, 5432)
    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Host "Port $port : LISTENING" -ForegroundColor Green
        } else {
            Write-Host "Port $port : NOT LISTENING" -ForegroundColor Red
        }
    }
}

function Show-Success {
    Write-Host "=========================================" -ForegroundColor Green
    Write-Success "🎉 Backend service deployment completed!"
    Write-Host "Backend API: http://localhost:8090" -ForegroundColor Cyan
    Write-Host "Database: localhost:5432" -ForegroundColor Cyan
    Write-Host "Default Account: admin/admin123" -ForegroundColor Cyan
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host "API Testing:" -ForegroundColor Yellow
    Write-Host "CAPTCHA: curl http://localhost:8090/captchaImage" -ForegroundColor White
    Write-Host "Health: curl http://localhost:8090/actuator/health" -ForegroundColor White
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host "Script Usage:" -ForegroundColor Yellow
    Write-Host ".\ccblife.ps1          # Docker deployment" -ForegroundColor White
    Write-Host ".\ccblife.ps1 jar      # JAR deployment" -ForegroundColor White
    Write-Host ".\ccblife.ps1 jar -Watch # JAR with log monitoring" -ForegroundColor White
    Write-Host ".\ccblife.ps1 stop     # Stop services" -ForegroundColor White
    Write-Host ".\ccblife.ps1 status   # Check status" -ForegroundColor White
    Write-Host "=========================================" -ForegroundColor Green
}

# Main script logic
Show-Header

switch ($Action.ToLower()) {
    "docker" {
        if (Deploy-Docker) {
            Show-Success
        } else {
            Write-Error "Docker deployment failed!"
            exit 1
        }
    }
    "jar" {
        Deploy-Jar
    }
    "stop" {
        Stop-Services
    }
    "status" {
        Show-Status
    }
    default {
        if (Deploy-Docker) {
            Show-Success
        } else {
            Write-Error "Docker deployment failed!"
            exit 1
        }
    }
}
