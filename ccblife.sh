#!/bin/bash

# CcbLife后端服务部署脚本
# 支持Docker和传统JAR两种部署方式
#
# 使用方法:
#   ./ccblife.sh          # Docker部署(推荐)
#   ./ccblife.sh docker   # Docker部署
#   ./ccblife.sh jar      # 传统JAR部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================="
echo "CcbLife 后端服务部署脚本"
echo "========================================="
echo "1. Docker部署 (推荐)"
echo "2. 传统JAR部署"
echo "========================================="

# 检查部署方式参数
DEPLOY_MODE=${1:-"docker"}

if [ "$DEPLOY_MODE" = "jar" ]; then
    print_info "使用传统JAR部署模式"
    # 检查Java环境
    if ! command -v java &> /dev/null; then
        print_error "Java未安装，请先安装Java 8+"
        exit 1
    fi

    # 检查JAR文件
    if [ ! -f "ccblife-admin/target/ccblife-admin.jar" ]; then
        print_warning "JAR文件不存在，开始构建..."
        mvn clean package -DskipTests
        if [ $? -ne 0 ]; then
            print_error "项目构建失败"
            exit 1
        fi
    fi

    # 启动JAR
    print_info "启动后端服务..."
    cd ccblife-admin/target
    nohup java -Duser.timezone=Asia/Shanghai -Djava.awt.headless=true -Xms512m -Xmx1024m -jar ccblife-admin.jar > ../../logs/app.log 2>&1 &
    print_success "后端服务已启动，日志文件: logs/app.log"
    print_info "访问地址: http://localhost:8090"
    exit 0
fi

print_info "使用Docker部署模式"

# 检查Docker和Docker Compose
print_info "检查Docker环境..."
if ! command -v docker &> /dev/null; then
    print_error "Docker未安装，请先安装Docker"
    print_info "安装命令: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose未安装，请先安装Docker Compose"
    print_info "安装命令: sudo curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
    exit 1
fi

print_success "Docker环境检查通过"

# 检查必要文件
print_info "检查项目文件..."
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml文件不存在"
    exit 1
fi

if [ ! -f "ccblife-admin/target/ccblife-admin.jar" ]; then
    print_warning "ccblife-admin.jar文件不存在，将自动构建项目"
    print_info "开始构建后端项目..."
    mvn clean package -DskipTests
    if [ $? -ne 0 ]; then
        print_error "后端项目构建失败"
        exit 1
    fi
    print_success "后端项目构建完成"
fi

print_success "项目文件检查通过"

# 停止现有容器
print_info "停止现有容器..."
docker-compose down --remove-orphans

# 清理旧镜像
print_info "清理旧镜像..."
docker image prune -f

# 构建并启动服务
print_info "构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
print_info "等待服务启动..."
sleep 30

# 检查服务状态
print_info "检查服务状态..."
docker-compose ps

# 健康检查
print_info "执行健康检查..."
for i in {1..30}; do
    if curl -f http://localhost:8090/captchaImage > /dev/null 2>&1; then
        print_success "后端服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "后端服务启动失败"
        print_info "查看服务日志:"
        docker-compose logs ccblife-app
        exit 1
    fi
    print_info "等待后端服务启动... ($i/30)"
    sleep 2
done

# 检查数据库连接
print_info "检查数据库连接..."
if docker exec ccblife-postgres pg_isready -U postgres > /dev/null 2>&1; then
    print_success "数据库连接正常"
else
    print_error "数据库连接失败"
    docker-compose logs postgres
fi

echo "========================================="
print_success "🎉 后端服务部署完成！"
echo "后端API地址: http://localhost:8090"
echo "数据库地址: localhost:5432"
echo "默认账号: admin/admin123"
echo "========================================="
echo "API测试:"
echo "验证码接口: curl http://localhost:8090/captchaImage"
echo "健康检查: curl http://localhost:8090/actuator/health"
echo "========================================="
echo "部署脚本使用方法:"
echo "./ccblife.sh          # Docker部署(推荐)"
echo "./ccblife.sh docker   # Docker部署"
echo "./ccblife.sh jar      # 传统JAR部署"
echo "========================================="
echo "Docker常用命令:"
echo "查看日志: docker-compose logs -f [ccblife-app|postgres]"
echo "重启服务: docker-compose restart [服务名]"
echo "停止服务: docker-compose down"
echo "进入数据库: docker exec -it ccblife-postgres psql -U postgres -d ccblife"
echo "========================================="
